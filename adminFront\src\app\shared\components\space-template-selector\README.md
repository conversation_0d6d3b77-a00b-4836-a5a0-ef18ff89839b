# 空間模板選擇器 - nb-dialog 優化版本

## 概述
此組件已經從自定義模態視窗升級為使用 Nebular 的 `nb-dialog`，提供更好的使用者體驗和更統一的設計風格。

## 主要變更

### 1. 架構變更
- **之前**: 使用自定義模態視窗實現
- **之後**: 使用 `NbDialogService` 和 `nb-dialog` 元件

### 2. 組件結構
```
SpaceTemplateSelectorComponent
├── 使用 nb-card 作為主容器
├── nb-card-header 包含標題和關閉按鈕
├── nb-card-body 包含主要內容
└── nb-card-footer 包含操作按鈕
```

### 3. 依賴模組
組件現在導入以下 Nebular 模組：
- `NbCardModule`
- `NbButtonModule`
- `NbIconModule`
- `NbCheckboxModule`
- `NbSelectModule` (新增：用於模板類型選擇)

### 4. 新功能：模板類型篩選
- 新增 CTemplateType 選擇器，可以篩選顯示空間模板或項目模板
- 支援動態切換模板類型，自動重新載入對應的模板資料
- 使用 EnumTemplateType 枚舉來管理模板類型

### 5. 新功能：單價和單位顯示
- 在確認套用步驟中顯示模板明細項目的單價和單位資訊
- 單價以 NT$ 格式顯示，支援小數點後兩位
- 單位資訊以圖標和文字形式顯示
- 支援深色主題的樣式適配

## 使用方式

### 方法 1: 使用按鈕元件 (推薦，最簡單)
```html
<app-space-template-selector-button
  [buildCaseId]="yourBuildCaseId"
  [CTemplateType]="EnumTemplateType.SpaceTemplate"
  [text]="'模板新增'"
  [icon]="'fas fa-layer-group'"
  [buttonClass]="'btn btn-warning mr-2'"
  [disabled]="!yourBuildCaseId"
  (templateApplied)="onTemplateApplied($event)"
  (error)="onTemplateError($event)">
</app-space-template-selector-button>
```

```typescript
import { SpaceTemplateSelectorButtonComponent } from './space-template-selector-button.component';
import { SpaceTemplateConfig } from './space-template-selector.component';

@Component({
  imports: [SpaceTemplateSelectorButtonComponent]
})
export class YourComponent {
  onTemplateApplied(config: SpaceTemplateConfig) {
    // 處理模板套用
  }
  
  onTemplateError(error: string) {
    // 處理錯誤
  }
}
```

### 方法 2: 使用服務
```typescript
import { SpaceTemplateSelectorService } from './space-template-selector.service';

constructor(private templateSelectorService: SpaceTemplateSelectorService) {}

openTemplateSelector() {
  this.templateSelectorService.openSelector('buildCaseId')
    .subscribe(result => {
      if (result) {
        // 處理模板套用
        console.log('選擇的模板:', result);
      }
    });
}
```

### 方法 3: 直接使用 NbDialogService
```typescript
import { NbDialogService } from '@nebular/theme';
import { SpaceTemplateSelectorComponent } from './space-template-selector.component';
import { EnumTemplateType } from '../../enum/enumTemplateType';

constructor(private dialogService: NbDialogService) {}

openTemplateSelector() {
  const dialogRef = this.dialogService.open(SpaceTemplateSelectorComponent, {
    context: {
      buildCaseId: 'your-build-case-id',
      CTemplateType: EnumTemplateType.SpaceTemplate // 指定模板類型
    },
    closeOnBackdropClick: true,
    closeOnEsc: true
  });

  dialogRef.componentRef.instance.templateApplied.subscribe(config => {
    // 處理模板套用
    dialogRef.close();
  });
}
```

## API 變更

### 新增的按鈕元件
- `SpaceTemplateSelectorButtonComponent` - 獨立的按鈕元件
  - 輸入: `buildCaseId`, `text`, `icon`, `buttonClass`, `disabled`, `config`
  - 輸出: `templateApplied`, `beforeOpen`, `error`

### 新增的服務配置
- `SpaceTemplateSelectorConfig` - 服務配置介面
  - `buildCaseId?: string` - 建案ID
  - `buttonText?: string` - 按鈕文字
  - `buttonIcon?: string` - 按鈕圖標
  - `buttonClass?: string` - 按鈕樣式類別
  - `dialogTitle?: string` - 對話框標題

### 新增的輔助類別
- `SpaceTemplateSelectorHelper` - 提供快速配置方法

### 移除的屬性
- `isVisible: boolean` - 不再需要，由 `NbDialogService` 管理
- `closed: EventEmitter<void>` - 使用 `dialogRef.onClose` 替代

### 移除的方法
- `open()` - 使用 `SpaceTemplateSelectorService.openSelector()` 或 `NbDialogService.open()` 替代
- `onBackdropClick()` - 由 `NbDialogService` 自動處理

### 新增的依賴
- `NbDialogRef<SpaceTemplateSelectorComponent>` - 注入對話框引用

## 樣式變更

### CSS 類別重構
- 移除 `.space-template-modal` 和 `.space-template-content`
- 新增 `.space-template-dialog` 作為主容器
- 使用 Nebular 的 CSS 變數確保主題一致性

### 圖示更新
- FontAwesome 圖示 (`fas fa-*`) 替換為 Nebular 圖示 (`nb-icon`)
- 確保圖示樣式與主題配色一致

## 優勢

1. **更好的可訪問性**: `nb-dialog` 提供內建的可訪問性支持
2. **主題一致性**: 自動適應 Nebular 主題配色
3. **更少的程式碼**: 不需要手動管理模態狀態
4. **更好的使用者體驗**: 內建動畫和互動效果
5. **移動裝置友善**: 響應式設計

## 遷移指南

如果您的專案正在使用舊版本的組件：

1. 更新組件導入
2. 替換開啟對話框的方法
3. 移除 `isVisible` 相關的程式碼
4. 更新事件處理器

### 舊版本
```typescript
// 舊的方式
<app-space-template-selector 
  [isVisible]="showTemplateSelector"
  (templateApplied)="onTemplateApplied($event)"
  (closed)="onTemplateSelectorClosed()">
</app-space-template-selector>
```

### 新版本
```typescript
// 新的方式
openTemplateSelector() {
  this.templateSelectorService.openSelector()
    .subscribe(result => {
      if (result) this.onTemplateApplied(result);
    });
}
```

## 注意事項

1. 確保您的應用模組中已導入 `NbDialogModule.forRoot()`
2. 組件現在是 standalone，可以直接在其他 standalone 組件中使用
3. 服務使用 `providedIn: 'root'`，自動在根級別提供
