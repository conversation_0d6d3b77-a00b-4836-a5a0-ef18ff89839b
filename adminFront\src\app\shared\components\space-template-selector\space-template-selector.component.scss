// 導入主題色彩變數
@import '../../../@theme/styles/_colors';

/* 空間模板選擇器 - nb-dialog 版本樣式 */
.space-template-dialog {
  min-width: 600px;
  max-width: 800px;
  min-height: 500px;
  max-height: 80vh;

  .space-template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid $border-light;
    background-color: $bg-primary;

    .space-template-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: $text-primary;
    }

    .close-btn {
      padding: 0.25rem;
      min-width: auto;
      border: none;
      background: transparent;
      color: $text-muted;
      transition: $transition-fast;

      &:hover {
        color: $text-primary;
        background-color: $bg-hover;
        border-radius: 0.25rem;
      }

      nb-icon {
        font-size: 1.25rem;
      }
    }
  }

  .space-template-body {
    padding: 1.5rem;
    overflow-y: auto;
    max-height: 60vh;
    background-color: $bg-primary;

    /* 步驟導航 */
    .step-nav {
      display: flex;
      justify-content: center;
      margin-bottom: 2rem;
      border-bottom: 1px solid $border-light;
      padding-bottom: 1rem;

      .step-item {
        padding: 0.5rem 1rem;
        margin: 0 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        transition: $transition-normal;

        &.active {
          background: $gradient-primary;
          color: $text-light;
          box-shadow: $shadow-sm;
        }

        &.completed {
          background-color: $success-base;
          color: $text-light;
          box-shadow: $shadow-sm;
        }

        &.pending {
          background-color: $bg-secondary;
          color: $text-muted;
          border: 1px solid $border-light;
        }
      }
    }

    /* 步驟內容 */
    .step-content {
      min-height: 300px;

      .section-title {
        display: flex;
        align-items: center;
        font-size: 1.1rem;
        font-weight: 600;
        color: $text-primary;
        margin-bottom: 1rem;

        nb-icon {
          margin-right: 0.5rem;
          color: $primary-gold-base;
        }
      }

      /* 模板選擇區域 */
      .template-selection {

        /* 模板類型選擇器 */
        .template-type-selector {
          margin-bottom: 1.5rem;
          padding: 1rem;
          background-color: $bg-secondary;
          border: 1px solid $border-light;
          border-radius: 0.375rem;

          .selector-label {
            display: flex;
            align-items: center;
            font-size: 1rem;
            font-weight: 600;
            color: $text-primary;
            margin-bottom: 0.75rem;

            nb-icon {
              margin-right: 0.5rem;
              color: $primary-gold-base;
            }
          }

          .template-type-select {
            width: 100%;
            max-width: 300px;

            nb-select {
              width: 100%;
            }
          }
        }

        .template-list {
          .template-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid $border-light;
            border-radius: 0.375rem;
            transition: $transition-normal;
            background-color: $bg-primary;
            box-shadow: $shadow-sm;

            &:hover {
              border-color: $primary-gold-base;
              box-shadow: $shadow-md;
              background-color: $bg-light-gold;
            }

            nb-checkbox {
              width: 100%;

              .template-info {
                margin-left: 0.5rem;
                flex: 1;

                .item-name {
                  font-weight: 600;
                  color: $text-primary;
                  margin-bottom: 0.25rem;
                }

                .item-code {
                  font-size: 0.875rem;
                  color: $text-muted;
                  margin-bottom: 0.25rem;
                }

                .item-status {
                  font-size: 0.875rem;
                  color: $text-secondary;
                  margin-bottom: 0.25rem;
                }

                .item-type {
                  font-size: 0.875rem;
                  color: $text-secondary;
                }
              }
            }
          }

          .no-templates {
            text-align: center;
            padding: 2rem;
            color: $text-muted;
            background-color: $bg-secondary;
            border-radius: 0.375rem;
            border: 1px solid $border-light;

            nb-icon {
              margin-right: 0.5rem;
              color: $info-base;
            }
          }
        }
      }

      /* 確認套用區域 */
      .confirmation-area {
        .selected-summary {
          background: $gradient-primary-light;
          border: 1px solid $border-primary;
          border-radius: 0.375rem;
          padding: 1rem;
          margin-bottom: 1.5rem;

          .summary-text {
            color: $text-primary;
            font-weight: 500;

            strong {
              color: $primary-gold-dark;
            }
          }
        }

        .selected-templates-details {
          .template-detail-section {
            border: 1px solid $border-light;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            overflow: hidden;
            background-color: $bg-primary;
            box-shadow: $shadow-sm;

            .template-detail-header {
              background: $gradient-primary-light;
              padding: 1rem;
              border-bottom: 1px solid $border-light;

              .template-name {
                margin: 0 0 0.5rem 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: $text-primary;
              }

              .template-meta {
                display: flex;
                gap: 1rem;

                .template-id {
                  font-size: 0.875rem;
                  color: $text-muted;
                }

                .template-status {
                  font-size: 0.875rem;
                  color: $success-base;
                  font-weight: 500;
                }
              }
            }

            .template-detail-content {
              padding: 1rem;

              .detail-items-header {
                margin-bottom: 0.75rem;

                .detail-count {
                  font-weight: 500;
                  color: $text-secondary;
                }
              }

              .detail-items-list {
                .detail-item {
                  display: flex;
                  align-items: flex-start;
                  padding: 0.5rem 0;
                  border-bottom: 1px solid $border-light;

                  &:last-child {
                    border-bottom: none;
                  }

                  .detail-index {
                    background: $gradient-primary;
                    color: $text-light;
                    border-radius: 50%;
                    width: 1.5rem;
                    height: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.75rem;
                    font-weight: 600;
                    margin-right: 0.75rem;
                    flex-shrink: 0;
                  }

                  .detail-info {
                    flex: 1;

                    .detail-part {
                      font-weight: 500;
                      color: $text-primary;
                      margin-bottom: 0.25rem;
                    }

                    .detail-location {
                      font-size: 0.875rem;
                      color: $text-muted;
                    }
                  }
                }
              }

              .no-details {
                text-align: center;
                padding: 1rem;
                color: $text-muted;
                background-color: $bg-secondary;
                border-radius: 0.375rem;

                nb-icon {
                  margin-right: 0.5rem;
                  color: $info-base;
                }
              }
            }
          }
        }

        .conflict-warning {
          background-color: $warning-light;
          border: 1px solid $warning-base;
          border-radius: 0.375rem;
          padding: 1rem;
          margin-top: 1rem;

          .warning-text {
            color: $warning-dark;
            font-weight: 500;

            nb-icon {
              margin-right: 0.25rem;
              color: $warning-base;
            }
          }
        }
      }
    }
  }

  .space-template-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid $border-light;
    background-color: $bg-secondary;

    .progress-info {
      color: $text-muted;
      font-size: 0.875rem;
    }

    .step-buttons {
      display: flex;
      gap: 0.75rem;

      button {
        min-width: 80px;
        transition: $transition-fast;

        &[nbButton][status="primary"] {
          background: $btn-primary-bg;
          border-color: $primary-gold-base;
          color: $text-light;

          &:hover:not(:disabled) {
            background: $btn-primary-hover;
            transform: translateY(-1px);
            box-shadow: $shadow-md;
          }
        }

        &[nbButton][status="success"] {
          background-color: $success-base;
          border-color: $success-base;
          color: $text-light;

          &:hover:not(:disabled) {
            background-color: $success-dark;
            border-color: $success-dark;
            transform: translateY(-1px);
            box-shadow: $shadow-md;
          }
        }

        &[nbButton][status="basic"] {
          background-color: transparent;
          border-color: $border-medium;
          color: $text-secondary;

          &:hover:not(:disabled) {
            background-color: $bg-hover;
            border-color: $text-secondary;
            color: $text-primary;
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .space-template-dialog {
    min-width: 95vw;
    max-width: 95vw;
    margin: 0.5rem;

    .space-template-body {
      padding: 1rem;

      .step-nav {
        .step-item {
          font-size: 0.875rem;
          padding: 0.4rem 0.8rem;
          margin: 0 0.25rem;
        }
      }

      .step-content {
        .template-selection {
          .template-list {
            .template-item {
              padding: 0.75rem;

              nb-checkbox {
                .template-info {
                  .item-name {
                    font-size: 0.9rem;
                  }

                  .item-code,
                  .item-status,
                  .item-type {
                    font-size: 0.8rem;
                  }
                }
              }
            }
          }
        }
      }
    }

    .space-template-footer {
      flex-direction: column;
      gap: 0.75rem;
      align-items: stretch;

      .step-buttons {
        justify-content: center;
      }
    }
  }
}

/* nb-dialog 內容區域調整 */
:host {
  display: block;
  width: 100%;
}

/* 確保 nb-checkbox 正常顯示 */
nb-checkbox {
  display: flex;
  align-items: flex-start;
  width: 100%;

  .customised-control-input {
    margin-right: 0.75rem;
    margin-top: 0.125rem;
  }
}

/* 工具類別 */
.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

/* 深色主題支援 */
:host-context(.nb-theme-dark) {
  .space-template-dialog {
    .space-template-header {
      background-color: $dark-bg-primary;
      border-bottom-color: $dark-border;

      .space-template-title {
        color: $dark-text-primary;
      }

      .close-btn {
        color: $dark-text-secondary;

        &:hover {
          color: $dark-text-primary;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .space-template-body {
      background-color: $dark-bg-primary;

      .step-nav {
        border-bottom-color: $dark-border;
      }

      .step-content {
        .section-title {
          color: $dark-text-primary;
        }

        .template-selection {
          .template-type-selector {
            background-color: $dark-bg-secondary;
            border-color: $dark-border;

            .selector-label {
              color: $dark-text-primary;
            }
          }

          .template-list {
            .template-item {
              background-color: $dark-bg-secondary;
              border-color: $dark-border;

              &:hover {
                box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
              }

              .template-info {
                .item-name {
                  color: $dark-text-primary;
                }

                .item-code,
                .item-status,
                .item-type {
                  color: $dark-text-secondary;
                }
              }
            }

            .no-templates {
              background-color: $dark-bg-secondary;
              border-color: $dark-border;
              color: $dark-text-secondary;
            }
          }
        }
      }
    }

    .space-template-footer {
      background-color: $dark-bg-secondary;
      border-top-color: $dark-border;

      .progress-info {
        color: $dark-text-secondary;
      }
    }
  }
}
